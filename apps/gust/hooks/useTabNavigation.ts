'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';

interface UseTabNavigationProps {
  validTabs: string[];
  defaultTab: string;
  paramName?: string;
}

interface UseTabNavigationReturn {
  activeTab: string;
  handleTabChange: (value: string) => void;
  setTabFromExternal: (tab: string) => void;
}

export const useTabNavigation = ({
  validTabs,
  defaultTab,
  paramName = 'tab'
}: UseTabNavigationProps): UseTabNavigationReturn => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  
  // Local state for active tab
  const [activeTab, setActiveTab] = useState(defaultTab);
  
  // Track if component has mounted to handle external navigation
  const [isMounted, setIsMounted] = useState(false);

  // Effect to track component mount
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Effect to handle initial load with tab parameters from external navigation
  useEffect(() => {
    const currentTab = searchParams.get(paramName);

    // On initial load, if we have a valid tab parameter, set it in local state
    if (currentTab && validTabs.includes(currentTab)) {
      setActiveTab(currentTab);
      return;
    }

    // If no tab or invalid tab on initial load, set to default
    if (!currentTab || !validTabs.includes(currentTab)) {
      setActiveTab(defaultTab);
      const params = new URLSearchParams(searchParams.toString());
      params.set(paramName, defaultTab);
      replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, []); // Only run on initial mount

  // Effect to handle URL changes and update tab accordingly
  // This will trigger when navigating from external sources (like header menu)
  useEffect(() => {
    if (!isMounted) return; // Don't run on initial mount

    const currentTab = searchParams.get(paramName);

    // If no tab param exists, set default
    if (!currentTab) {
      setActiveTab(defaultTab);
      const params = new URLSearchParams(searchParams.toString());
      params.set(paramName, defaultTab);
      replace(`${pathname}?${params.toString()}`, { scroll: false });
      return;
    }

    // Validate that the tab parameter is one of the allowed values
    if (!validTabs.includes(currentTab)) {
      setActiveTab(defaultTab);
      const params = new URLSearchParams(searchParams.toString());
      params.set(paramName, defaultTab);
      replace(`${pathname}?${params.toString()}`, { scroll: false });
    } else {
      // Update local state with valid tab from URL
      setActiveTab(currentTab);
    }
  }, [isMounted, searchParams.toString(), pathname, replace, validTabs, defaultTab, paramName]);

  // Handler for tab changes (internal navigation)
  const handleTabChange = (value: string) => {
    if (!validTabs.includes(value)) {
      console.warn(`Invalid tab: ${value}. Valid tabs are: ${validTabs.join(', ')}`);
      return;
    }

    setActiveTab(value);
    const params = new URLSearchParams(searchParams.toString());
    params.set(paramName, value);
    replace(`${pathname}?${params.toString()}`);
  };

  // Function to set tab from external sources (like header navigation)
  const setTabFromExternal = (tab: string) => {
    if (!validTabs.includes(tab)) {
      console.warn(`Invalid tab: ${tab}. Valid tabs are: ${validTabs.join(', ')}`);
      return;
    }

    const params = new URLSearchParams(searchParams.toString());
    params.set(paramName, tab);
    replace(`${pathname}?${params.toString()}`);
    // Note: setActiveTab will be called by the useEffect that watches URL changes
  };

  return {
    activeTab,
    handleTabChange,
    setTabFromExternal
  };
};

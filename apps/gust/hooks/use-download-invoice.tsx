import { useCallback, useEffect, useRef, useState } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import Logo from '@/components/icons/logo';
import { useGetInvoiceDetails } from '@/queries/host-pages/invoices';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import SVGDisplay from '@/components/hostPages/common/SVGDisplay';
import { DownloadBtn } from '@/components/hostPages/common';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import { cn } from '@/lib/utils';
function formatIssueDate(inputDate) {
  if (!inputDate) return;
  return inputDate.slice(0, 10).replace(/\//g, '-');
}

const useDownloadPdf = (id, dialog: boolean = false) => {
  const [localData, setLocalData] = useState(null);
  const [shouldDownload, setShouldDownload] = useState(false);

  console.log({ id });
  const { data, refetch, isPending } = useGetInvoiceDetails(id);
  const currentLang = useCurrentLocale();
  const [isDownloading, setIsDownloading] = useState(false);
  const pdfRef = useRef(null);
  const t = useScopedI18n('hostPages.invoicesPage');
  useEffect(() => {
    if (typeof window === 'undefined') return;
  }, []);
  const currentModalData = localData || data?.data?.data;

  const contentId = `content-to-download-${id}`; // Unique ID based on invoice id

  useEffect(() => {
    if (shouldDownload && data) {
      const generatePdf = async () => {
        setIsDownloading(true);

        if (pdfRef.current) {
          try {
            const canvas = await html2canvas(pdfRef.current);
            const pdf = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' });
            pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 10, 10, 190, (canvas.height * 190) / canvas.width);
            pdf.save('invoice.pdf');
          } catch (error) {
            console.error('Error generating PDF:', error);
          } finally {
            setIsDownloading(false);
            setShouldDownload(false);
          }
        }
      };

      generatePdf();
    }
  }, [data, shouldDownload, id]);

  const downloadPdfFromHtml = useCallback(async () => {
    try {
      const { data: freshData } = await refetch();
      setLocalData(freshData?.data?.data);
      setShouldDownload(true);
    } catch (error) {
      console.error('Error fetching invoice:', error);
    }
  }, [refetch]);

  console.log({
    isPending,
    isDownloading,
    shouldDownload,
  });

  const Invoice = currentModalData ? (
    <div className="m-auto max-w-[1100px]">
      <div id={contentId} className="p-10">
        <div className="mb-5 flex w-full items-center justify-between">
          <div className="ml-4 flex flex-col">
            <h1 className="text-secondary text-[32px] font-bold">TAX INVOICE</h1>
            <p className="text-secondary text-[32px] font-bold">فاتورة ضريبية</p>
          </div>
          <Logo />
        </div>

        <div className="scrollable-content overflow-y-auto py-6">
          <div className="flex items-stretch justify-between gap-6">
            <div className="flex-1">
              <h2 className="text-primary text-[18px] font-bold">{t('invoice_from')}</h2>

              {currentModalData?.company_date?.name && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('name')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.company_date?.name}</div>
                </div>
              )}
              {currentModalData?.company_date?.address && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('address')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.company_date?.address}</div>
                </div>
              )}
              {currentModalData?.company_date?.tax_number && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('tax_number')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.company_date?.tax_number}</div>
                </div>
              )}
              {currentModalData?.company_date?.commercial_number && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('commercial_number')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">
                    {currentModalData?.company_date?.commercial_number}
                  </div>
                </div>
              )}
            </div>
            <div className=" w-[0.6px] shrink-0 bg-[#D8D8D8]"></div>

            <div className="flex-1">
              <h2 className="text-primary text-[18px] font-bold">{t('invoice_to')}</h2>

              {currentModalData?.user_date?.user_name && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('name')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.user_date?.user_name}</div>
                </div>
              )}
              {currentModalData?.user_date?.user_address && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('address')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.user_date?.user_address}</div>
                </div>
              )}
              {currentModalData?.user_date?.user_tax_number && (
                <div className="mt-4 flex">
                  <div className="text-secondary flex-[1] text-[14px] font-bold">{t('tax_number')}</div>
                  <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.user_date?.user_tax_number}</div>
                </div>
              )}
            </div>
          </div>

          <div className=" my-6 h-px w-full bg-[#D8D8D8]"></div>

          <h2 className="text-primary text-[18px] font-bold">{t('invoice_details')}</h2>

          <div className="flex items-stretch justify-between gap-[38px]">
            <div className="flex-1">
              <div className="mt-4 flex gap-4">
                <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('invoice_number')}</div>
                <div className="text-[14px] text-[#333]">{currentModalData?.code}</div>
              </div>
              <div className="mt-4 flex gap-4">
                <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('invoice_type')}</div>
                <div className="text-[14px] text-[#333]">{t(currentModalData?.invoice_type)}</div>
              </div>
            </div>

            <div className=" w-[0.6px] shrink-0 bg-[#D8D8D8]"></div>

            <div className="flex-1">
              <div className="mt-4 flex gap-4">
                <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('created_date')}</div>
                <div className="text-[14px] text-[#333]">{formatIssueDate(currentModalData?.issue_date)}</div>
              </div>
              <div className="mt-4 flex gap-4">
                <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('due_date')}</div>
                <div className="text-[14px] text-[#333]">{formatIssueDate(currentModalData?.issue_date)}</div>
              </div>
            </div>
            <div className=" w-[0.6px] shrink-0 bg-[#D8D8D8]"></div>
            <div className="flex-1">
              <div className="mt-4 flex gap-4">
                <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('payment_way')}</div>
                <div className="text-[14px] text-[#333]">-</div>
              </div>
              <div className="mt-4 flex gap-4">
                <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('amount_due')}</div>
                <div className="text-[14px] text-[#333]">
                  0.0{' '}
                  {currentLang === 'ar' ? (
                    <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#333]" size={15} />
                  ) : (
                    t('riyal')
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className=" my-6 h-px w-full bg-[#D8D8D8]"></div>

          <Table className="table-auto">
            <TableHeader>
              <TableRow className="bg-[#F9F9FB] hover:bg-[#F9F9FB]">
                <TableHead className="px-4 text-[#335C87]">#</TableHead>
                <TableHead className="w-1/4 px-4 text-start text-[#335C87]">{t('product')}</TableHead>
                <TableHead className="px-4 text-[#335C87]">{t('quantity')}</TableHead>
                <TableHead className="px-4 text-[#335C87]">{t('unit_price')}</TableHead>
                <TableHead className="px-4 text-[#335C87]">{t('total_before_tax')}</TableHead>
                <TableHead className="px-4 text-[#335C87]">{t('tax_percentage_symbol')}</TableHead>
                <TableHead className="px-4 text-[#335C87]">{t('tax')}</TableHead>
                <TableHead className="px-4 text-[#335C87]">{t('total_value')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className=" py-4 align-text-top text-[14px] text-[#335C87]">1</TableCell>
                <TableCell className=" py-4 text-start">
                  <div>
                    <h3 className="text-base font-bold text-[#335C87]">رسوم حجز رقم #{currentModalData?.related_id}</h3>
                    <p className="mt-3 text-[14px] text-[#6F6F6F]">
                      {t('property_name')}: {currentModalData?.company_date?.name}
                      <br />
                      {t('address')}: {currentModalData?.company_date?.address}
                    </p>
                  </div>
                </TableCell>
                <TableCell className=" py-4 align-text-top">
                  {currentModalData?.invoice_products[0]?.quantity}
                </TableCell>
                <TableCell className=" py-4 align-text-top">
                  {currentModalData?.invoice_products[0]?.total_before_tax?.toFixed(2)}{' '}
                  {currentLang === 'ar' ? (
                    <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                  ) : (
                    t('riyal')
                  )}
                </TableCell>
                <TableCell className=" py-4 align-text-top">
                  {currentModalData?.invoice_products[0]?.total_before_tax?.toFixed(2)}{' '}
                  {currentLang === 'ar' ? (
                    <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                  ) : (
                    t('riyal')
                  )}
                </TableCell>
                <TableCell className=" py-4 align-text-top">
                  {currentModalData?.invoice_products[0]?.tax_percentage?.toFixed(2)}
                </TableCell>
                <TableCell className=" py-4 align-text-top">
                  {currentModalData?.invoice_products[0]?.total_tax?.toFixed(2)}{' '}
                  {currentLang === 'ar' ? (
                    <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                  ) : (
                    t('riyal')
                  )}
                </TableCell>
                <TableCell className=" py-4 align-text-top text-[#D84E67]">
                  {currentModalData?.invoice_products[0]?.total_after_tax?.toFixed(2)}{' '}
                  {currentLang === 'ar' ? (
                    <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#D84E67]" size={15} />
                  ) : (
                    t('riyal')
                  )}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>

          <div className=" my-6 h-px w-full bg-[#D8D8D8]"></div>

          <div className="flex items-center justify-between">
            <div className="qr-code">
              <div className="h-[156px] w-[156px] p-5">
                <SVGDisplay decode={true} svgString={currentModalData?.qr_code} />
              </div>
            </div>
            <div className="side-data">
              <div className="rounded-[12px] bg-[#F9F9FB] px-3 py-4">
                <div className="flex flex-col gap-4">
                  <div className="flex justify-between gap-6">
                    <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('total_before_tax')}</div>
                    <div className="text-[14px] text-[#333]">
                      {currentModalData?.total_before_tax}{' '}
                      {currentLang === 'ar' ? (
                        <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#333]" size={15} />
                      ) : (
                        t('riyal')
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between gap-6">
                    <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('total_tax')}</div>
                    <div className="text-[14px] text-[#333]">
                      {currentModalData?.total_tax}{' '}
                      {currentLang === 'ar' ? (
                        <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#333]" size={15} />
                      ) : (
                        t('riyal')
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between gap-6">
                    <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('total')}</div>
                    <div className="text-[14px] text-[#333]">
                      {currentModalData?.total_after_tax}{' '}
                      {currentLang === 'ar' ? (
                        <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#333]" size={15} />
                      ) : (
                        t('riyal')
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between gap-6">
                    <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('amount_due')}</div>
                    <div className="text-[14px] text-[#333]">
                      0.0{' '}
                      {currentLang === 'ar' ? (
                        <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#333]" size={15} />
                      ) : (
                        t('riyal')
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ) : null;
  const DownloadButton = (variant: any = 'link', className?: string) => (
    <>
      <div ref={pdfRef} style={{ position: 'absolute', left: '-9999px', top: '-9999px' }}>
        {Invoice}
      </div>

      {!dialog ? (
        <DownloadBtn
          loading={shouldDownload || isPending || isDownloading}
          variant={variant}
          onClick={downloadPdfFromHtml}
        />
      ) : (
        <Button
          loading={shouldDownload || isPending || isDownloading}
          variant="outline"
          className={cn(
            'gap-[6px] border border-[#56789B] bg-transparent px-5 py-2 text-[14px] text-[#56789B]',
            className
          )}
          onClick={downloadPdfFromHtml}>
          <IconWrapper name="DocumentDownload" size={16} />
          {t('download')}
        </Button>
      )}
    </>
  );

  return { isLoading: isPending || isDownloading, DownloadButton };
};

export default useDownloadPdf;

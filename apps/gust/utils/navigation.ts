'use client';

/**
 * Navigation utilities for tab-based routing
 */

/**
 * Navigate to a specific tab on the chat page
 * @param router - Next.js router instance
 * @param tab - The tab to navigate to ('chats' | 'notifications')
 * @param preserveParams - Whether to preserve existing URL parameters (default: true)
 */
export const navigateToChatTab = (
  router: any, // NextRouter type
  tab: 'chats' | 'notifications',
  preserveParams: boolean = true
) => {
  const validTabs = ['chats', 'notifications'];
  
  if (!validTabs.includes(tab)) {
    console.warn(`Invalid chat tab: ${tab}. Valid tabs are: ${validTabs.join(', ')}`);
    return;
  }

  const basePath = '/chat';
  
  if (preserveParams) {
    // Get current search params if we're preserving them
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.set('tab', tab);
    router.push(`${basePath}?${currentParams.toString()}`);
  } else {
    // Navigate with only the tab parameter
    router.push(`${basePath}?tab=${tab}`);
  }
};

/**
 * Generic function to navigate to any page with tab parameters
 * @param router - Next.js router instance
 * @param path - The base path to navigate to
 * @param tab - The tab to set
 * @param paramName - The parameter name for the tab (default: 'tab')
 * @param preserveParams - Whether to preserve existing URL parameters (default: true)
 */
export const navigateToTabPage = (
  router: any,
  path: string,
  tab: string,
  paramName: string = 'tab',
  preserveParams: boolean = true
) => {
  if (preserveParams) {
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.set(paramName, tab);
    router.push(`${path}?${currentParams.toString()}`);
  } else {
    router.push(`${path}?${paramName}=${tab}`);
  }
};

/**
 * Hook-like function to get navigation helpers
 * This can be used in components that need to navigate to tabs
 */
export const useTabNavigation = () => {
  return {
    navigateToChatTab,
    navigateToTabPage
  };
};

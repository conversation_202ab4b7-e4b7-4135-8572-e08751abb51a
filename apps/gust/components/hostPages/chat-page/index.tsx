'use client';

import type { ReactElement, FC } from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import TabsHeader from './tabs-header';
import HostChatsTab from './tabs/host-chats-tab';
import NotificationsHostTab from '../notifications/notification-host-tab';
import { useTabNavigation } from '@/hooks/useTabNavigation';

const ChatPage: FC = (): ReactElement => {
  const t = useScopedI18n('chat');

  // Use the tab navigation hook
  const { activeTab, handleTabChange } = useTabNavigation({
    validTabs: ['chats', 'notifications'],
    defaultTab: 'chats',
    paramName: 'tab'
  });

  return (
    <div className="bg-white-50 h-full">
      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="flex h-full flex-col">
        <TabsHeader />

        <TabsContent className="h-full flex-1 bg-transparent" value="chats">
          <HostChatsTab />
        </TabsContent>

        <TabsContent className="h-full flex-1 bg-transparent" value="notifications">
          <NotificationsHostTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChatPage;

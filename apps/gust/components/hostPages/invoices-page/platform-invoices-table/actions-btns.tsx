import useDownloadPdf from '@/hooks/use-download-invoice';
import { DisplayBtn, ShareBtn } from '../../common';

const ActionsBtns = ({ onDisplayClick, onShareClick, currentModalData }) => {
  console.log({ currentModalData });
  const { DownloadButton } = useDownloadPdf(currentModalData?.uuid, true);

  return (
    <div className="flex gap-3">
      <DisplayBtn onClick={onDisplayClick} />
      <DownloadButton />

      <ShareBtn onClick={onShareClick} />
    </div>
  );
};

export default ActionsBtns;

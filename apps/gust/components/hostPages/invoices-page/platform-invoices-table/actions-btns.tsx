import useDownloadPdf from '@/hooks/use-download-invoice';
import { DisplayBtn, ShareBtn } from '../../common';

interface ActionsBtnsProps {
  onDisplayClick: () => void;
  onShareClick: () => void;
  currentModalData: any;
}

const ActionsBtns = ({ onDisplayClick, onShareClick, currentModalData }: ActionsBtnsProps) => {
  const { DownloadButton } = useDownloadPdf(currentModalData?.uuid, true);

  return (
    <div className="flex gap-3">
      <DisplayBtn onClick={onDisplayClick} />
      <DownloadButton />

      <ShareBtn onClick={onShareClick} />
    </div>
  );
};

export default ActionsBtns;

import TableSkeletonLoader from '@/components/common/skeleton-loaders/table';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useCurrentLocale, useScopedI18n } from '@/lib/i18n/client-translator';
import { useEffect, useState } from 'react';
import Paginator from '@/components/common/paginator';
import { cn } from '@/lib/utils';
import ActionsBtns from './actions-btns';
import { EmptyTableFallback } from '../../common';
import { useGetInvoices } from '@/queries/host-pages/invoices';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import IconWrapper from '@/components/icons';
import SVGDisplay from '../../common/SVGDisplay';
import { useChangeQuery } from '../../my-properties-page/utils';
import { toast } from 'sonner';
import { ScrollArea } from '@/components/ui/scroll-area';
import useDownloadPdf from '@/hooks/use-download-invoice';
import { API_URL } from '@/constants';

function formatIssueDate(inputDate) {
  if (!inputDate) return;
  return inputDate.slice(0, 10).replace(/\//g, '-');
}

const InvoiceType = ({ invoice_type }) => {
  const t = useScopedI18n('hostPages.invoicesPage');

  const { label, bgColor, textColor } =
    {
      proforma: { label: t('proforma'), bgColor: '#EBEFF3', textColor: '#335C87' },
      tax: { label: t('tax_invoice'), bgColor: '#EBEFF3', textColor: '#335C87' },
      canceled: { label: t('canceled'), bgColor: '#FFC2C2', textColor: '#335C87' },
      refund: { label: t('refund'), bgColor: '#FFC2C2', textColor: '#335C87' },
    }[invoice_type] || {};

  return (
    <div
      style={{ backgroundColor: bgColor, color: textColor }}
      className={`text-secondary-300 text-md min-w-max max-w-max rounded-[4px] px-1 text-center`}>
      {label}
    </div>
  );
};

const MyInvoicesTable = () => {
  const { onChangeQuery, paramsWithValues } = useChangeQuery();
  const {
    active_tab,
    start_date,
    end_date,
    order_by,
    main_id,
    status,
    search_text,
    page: pageParam,
  } = paramsWithValues;

  const pageSize = 15;
  const [page, setPage] = useState<number>(pageParam ? parseInt(pageParam) : 1);

  const { refetch, isLoading, data, isError, isSuccess } = useGetInvoices({
    invoices_type: 'users',
    page: pageParam || '1',
    order_by,
    main_id,
    status,
    search_text,
    start_date,
    end_date,
  });

  useEffect(() => {
    if (isError) {
      toast.error(t('error_to_fetch_invoices'));
    }
  }, [data]);

  const t = useScopedI18n('hostPages.invoicesPage');
  const ct = useScopedI18n('currency');
  const [open, setOpen] = useState(false);
  const [currentModalData, setCurrentModalData] = useState<any>();
  const handleClose = () => setOpen(false);
  const { DownloadButton } = useDownloadPdf(currentModalData?.uuid, true);
  const currentLang = useCurrentLocale();
  const { pages, data: { invoices: listOfItems } = {} } = data?.data || {};
  const hasData = Boolean(listOfItems?.length);

  // Initialize currentModalData with first item when data is available
  useEffect(() => {
    if (hasData && listOfItems?.length > 0 && !currentModalData) {
      setCurrentModalData(listOfItems[0]);
    }
  }, [hasData, listOfItems, currentModalData]);

  const headCellClasses = 'text-start pe-8';
  const rowCellClasses = 'py-1.5 text-start pe-8 text-gray-400';

  const onDisplayClick = (index: number) => {
    setOpen(true);
    setCurrentModalData(listOfItems[index]);
  };

  useEffect(() => {
    refetch();
  }, [active_tab, end_date, order_by, main_id, status, search_text]);

  if (isLoading) {
    return <TableSkeletonLoader rows={7} />;
  }

  return (
    <ScrollArea>
      <div>
        <Table className="table-auto">
          <TableHeader>
            <TableRow className="hover:bg-gray-50">
              <TableHead className={cn(headCellClasses, 'pe-4 ps-4')}>#</TableHead>
              <TableHead className={headCellClasses}>{t('property_name')}</TableHead>
              <TableHead className={headCellClasses}>{t('reservation_number')}</TableHead>
              <TableHead className={cn(headCellClasses, 'min-w-max truncate')}>{t('reservation_date')}</TableHead>
              <TableHead className={cn(headCellClasses, 'min-w-max truncate')}>{t('amount')}</TableHead>
              <TableHead className={cn(headCellClasses, 'min-w-max truncate')}>{t('invoice_type')}</TableHead>
              <TableHead className={headCellClasses}>{t('actions')}</TableHead>
            </TableRow>
          </TableHeader>

          {hasData && (
            <TableBody>
              {listOfItems.map(
                ({ id, related_id, issue_date, total_after_tax, invoice_type, company_date, uuid }, index) => {
                  const itemNumber = (page - 1) * pageSize + (index + 1);
                  const [date] = issue_date?.split('-');
                  return (
                    <TableRow key={id + related_id}>
                      <TableCell className={cn(rowCellClasses, 'p-4')}>{itemNumber}</TableCell>

                      <TableCell
                        className={cn(rowCellClasses, 'text-secondary-300 w-fit max-w-64 truncate text-lg font-bold')}>
                        {company_date?.name}
                      </TableCell>

                      <TableCell className={cn(rowCellClasses, 'max-w-40 truncate underline')}>#{related_id}</TableCell>
                      <TableCell className={cn(rowCellClasses, 'max-w-40 truncate')}>{date}</TableCell>

                      <TableCell className={cn(rowCellClasses, 'max-w-40 truncate')}>
                        {total_after_tax}
                        {currentLang === 'ar' ? (
                          <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                        ) : (
                          ct('sar')
                        )}
                      </TableCell>

                      <TableCell className={rowCellClasses}>
                        <InvoiceType invoice_type={invoice_type} />
                      </TableCell>

                      <TableCell className={rowCellClasses}>
                        <ActionsBtns
                          onDisplayClick={() => {
                            onDisplayClick(index);
                          }}
                          onShareClick={() => {
                            navigator.clipboard.writeText(`${API_URL}/${currentLang}/invoice-download?uuid=${uuid}`);
                            toast.success(t('link_copied'));
                          }}
                          invoiceId={uuid}
                        />
                      </TableCell>
                    </TableRow>
                  );
                }
              )}
            </TableBody>
          )}
        </Table>

        {!hasData && <EmptyTableFallback Text={t('noInvoices')} className="min-h-[50vh]" />}
        <Paginator
          paginator={pages}
          page={isNaN(Number(pageParam)) ? 1 : Number(pageParam)}
          setPage={(page) => {
            onChangeQuery({ page: page.toString() });
            setPage(page);
          }}
          className="border-gray-75 mt-6 justify-start border-t border-solid pt-4"
        />
      </div>

      <Dialog open={open}>
        <DialogContent className="xs:!max-w-screen-xs mobile:!max-w-screen-mobile bottom-[-40%] max-h-[94%] min-h-fit grid-rows-[auto_1fr_auto] bg-[#FEFEFF] sm:!max-w-screen-sm md:!max-w-screen-md lg:!max-w-screen-lg xl:!max-w-screen-lg 2xl:!max-w-screen-lg ">
          <DialogHeader>
            <DialogTitle>{t('invoice_details')}</DialogTitle>
            <Button
              variant="outline"
              className="gap-[6px] bg-transparent px-5 py-2 text-sm text-[#56789B]"
              onClick={handleClose}>
              <div className="ltr:[transform:rotate(180deg)] rtl:[transform:rotate(0deg)]">
                <IconWrapper name="ArrowRightSmall" size={16} />
              </div>
              {t('back_to_invoices')}
            </Button>
          </DialogHeader>
          <div
            id={'content-to-download'}
            className="scrollable-content xs:max-h-[50vh] overflow-y-auto px-4 py-6 lg:max-h-[70vh]">
            <div className="flex items-stretch justify-between gap-6">
              {/* invoice from */}
              <div className="flex-1">
                <h2 className="text-primary text-[18px] font-bold">{t('invoice_from')}</h2>

                {currentModalData?.company_date?.name && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('name')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.company_date?.name}</div>
                  </div>
                )}
                {currentModalData?.company_date?.address && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('address')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.company_date?.address}</div>
                  </div>
                )}
                {currentModalData?.company_date?.tax_number && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('tax_number')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.company_date?.tax_number}</div>
                  </div>
                )}
                {currentModalData?.company_date?.commercial_number && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('commercial_number')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">
                      {currentModalData?.company_date?.commercial_number}
                    </div>
                  </div>
                )}
              </div>
              <div className=" w-[0.6px] shrink-0 bg-[#D8D8D8]"></div>

              {/* invoice to */}
              <div className="flex-1">
                <h2 className="text-primary text-[18px] font-bold">{t('invoice_to')}</h2>

                {currentModalData?.user_date?.user_name && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('name')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.user_date?.user_name}</div>
                  </div>
                )}
                {currentModalData?.user_date?.user_address && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('address')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">{currentModalData?.user_date?.user_address}</div>
                  </div>
                )}
                {currentModalData?.user_date?.user_tax_number && (
                  <div className="mt-4 flex">
                    <div className="text-secondary flex-[1] text-[14px] font-bold">{t('tax_number')}</div>
                    <div className="flex-[2] text-[14px] text-[#333]">
                      {currentModalData?.user_date?.user_tax_number}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className=" my-6 h-px w-full bg-[#D8D8D8]"></div>

            {/* invoice details */}
            <h2 className="text-primary text-[18px] font-bold">{t('invoice_details')}</h2>

            <div className="flex items-stretch justify-between gap-[38px]">
              <div className="flex-1">
                <div className="mt-4 flex gap-4">
                  <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('invoice_number')}</div>
                  <div className="text-[14px] text-[#333]">{currentModalData?.code}</div>
                </div>
                <div className="mt-4 flex gap-4">
                  <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('invoice_type')}</div>
                  <div className="text-[14px] text-[#333]">{t(currentModalData?.invoice_type)}</div>
                </div>
              </div>

              <div className=" w-[0.6px] shrink-0 bg-[#D8D8D8]"></div>

              <div className="flex-1">
                <div className="mt-4 flex gap-4">
                  <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('created_date')}</div>
                  <div className="text-[14px] text-[#333]">{formatIssueDate(currentModalData?.issue_date)}</div>
                </div>
                <div className="mt-4 flex gap-4">
                  <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('due_date')}</div>
                  <div className="text-[14px] text-[#333]">{formatIssueDate(currentModalData?.issue_date)}</div>
                </div>
              </div>
              <div className=" w-[0.6px] shrink-0 bg-[#D8D8D8]"></div>
              <div className="flex-1">
                <div className="mt-4 flex gap-4">
                  <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('payment_way')}</div>
                  <div className="text-[14px] text-[#333]">-</div>
                </div>
                <div className="mt-4 flex gap-4">
                  <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('amount_due')}</div>
                  <div className="text-[14px] text-[#333]">
                    0.0{' '}
                    {currentLang === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#333]" size={15} />
                    ) : (
                      t('riyal')
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className=" my-6 h-px w-full bg-[#D8D8D8]"></div>

            {/* Table */}
            {/* cn(headCellClasses, 'min-w-max truncate')) */}
            <Table className="table-auto">
              <TableHeader>
                <TableRow className="  bg-[#F9F9FB] hover:bg-[#F9F9FB]">
                  <TableHead className="px-4 text-[#335C87]">#</TableHead>
                  <TableHead className="w-1/4 px-4 text-start text-[#335C87]">{t('product')}</TableHead>
                  <TableHead className="px-4 text-[#335C87]">{t('quantity')}</TableHead>
                  <TableHead className="px-4 text-[#335C87]">{t('unit_price')}</TableHead>
                  <TableHead className="px-4 text-[#335C87]">{t('total_before_tax')}</TableHead>
                  <TableHead className="px-4 text-[#335C87]">{t('tax_percentage_symbol')}</TableHead>
                  <TableHead className="px-4 text-[#335C87]">{t('tax')}</TableHead>
                  <TableHead className="px-4 text-[#335C87]">{t('total_value')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className=" py-4 align-text-top text-[14px] text-[#335C87]">1</TableCell>
                  <TableCell className=" py-4 text-start">
                    <div>
                      <h3 className="text-base font-bold text-[#335C87]">
                        رسوم حجز رقم #{currentModalData?.related_id}
                      </h3>
                      <p className="mt-3 text-[14px] text-[#6F6F6F]">
                        {t('property_name')}: {currentModalData?.company_date?.name}
                        <br />
                        {t('address')}: {currentModalData?.company_date?.address}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell className=" py-4 align-text-top">
                    {currentModalData?.invoice_products[0]?.quantity}
                  </TableCell>
                  <TableCell className=" py-4 align-text-top">
                    {currentModalData?.invoice_products[0]?.total_before_tax?.toFixed(2)}{' '}
                    {currentLang === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                    ) : (
                      t('riyal')
                    )}
                  </TableCell>
                  <TableCell className=" py-4 align-text-top">
                    {currentModalData?.invoice_products[0]?.total_before_tax?.toFixed(2)}{' '}
                    {currentLang === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                    ) : (
                      t('riyal')
                    )}
                  </TableCell>
                  <TableCell className=" py-4 align-text-top">
                    {currentModalData?.invoice_products[0]?.tax_percentage?.toFixed(2)}
                  </TableCell>
                  <TableCell className=" py-4 align-text-top">
                    {currentModalData?.invoice_products[0]?.total_tax?.toFixed(2)}{' '}
                    {currentLang === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                    ) : (
                      t('riyal')
                    )}
                  </TableCell>
                  <TableCell className=" py-4 align-text-top text-[#D84E67]">
                    {currentModalData?.invoice_products[0]?.total_after_tax?.toFixed(2)}{' '}
                    {currentLang === 'ar' ? (
                      <IconWrapper name="Saudi_Riyal_Symbol" className="fill-[#D84E67]" size={15} />
                    ) : (
                      t('riyal')
                    )}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <div className=" my-6 h-px w-full bg-[#D8D8D8]"></div>

            {/* QR Code */}
            <div className="flex items-center justify-between">
              <div className="qr-code">
                <div className="h-[156px] w-[156px] p-5">
                  <SVGDisplay decode={true} svgString={currentModalData?.qr_code} />
                </div>
              </div>
              <div className="side-data">
                <div className="rounded-[12px] bg-[#F9F9FB] px-3 py-4">
                  <div className="flex flex-col gap-4">
                    <div className="flex justify-between gap-6">
                      <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('total_before_tax')}</div>
                      <div className="text-[14px] text-[#333]">
                        {currentModalData?.total_before_tax}{' '}
                        {currentLang === 'ar' ? (
                          <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                        ) : (
                          t('riyal')
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between gap-6">
                      <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('total_tax')}</div>
                      <div className="text-[14px] text-[#333]">
                        {currentModalData?.total_tax}{' '}
                        {currentLang === 'ar' ? (
                          <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                        ) : (
                          t('riyal')
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between gap-6">
                      <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('total')}</div>
                      <div className="text-[14px] text-[#333]">
                        {currentModalData?.total_after_tax}{' '}
                        {currentLang === 'ar' ? (
                          <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                        ) : (
                          t('riyal')
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between gap-6">
                      <div className="shrink-0 text-[14px] font-bold text-[#56789B]">{t('amount_due')}</div>
                      <div className="text-[14px] text-[#333]">
                        0.0{' '}
                        {currentLang === 'ar' ? (
                          <IconWrapper name="Saudi_Riyal_Symbol" className="fill-gray-400" size={15} />
                        ) : (
                          t('riyal')
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Modal footer */}
          <div>
            <div className="h-px w-full bg-[#D8D8D8]"></div>

            <div className="flex justify-start gap-3 px-6 py-4">
              <DownloadButton />
              <Button
                variant="outline"
                className="gap-[6px] border border-[#56789B] bg-transparent px-5 py-2 text-[14px] text-[#56789B]"
                onClick={() => {
                  navigator.clipboard.writeText(
                    `${API_URL}/${currentLang}/invoice-download?uuid=${currentModalData?.uuid}`
                  );
                  toast.success(t('link_copied'));
                }}>
                <IconWrapper name="ShareIcon" size={16} />
                {t('share')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </ScrollArea>
  );
};

export default MyInvoicesTable;

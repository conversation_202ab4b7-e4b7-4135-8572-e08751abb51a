import { DisplayBtn, ShareBtn } from '../../common';
import useDownloadPdf from '@/hooks/use-download-invoice';

interface ActionsBtnsProps {
  onDisplayClick: () => void;
  onShareClick: () => void;
  invoiceId: string;
}

const ActionsBtns = ({ onDisplayClick, onShareClick, invoiceId }: ActionsBtnsProps) => {
  const { DownloadButton } = useDownloadPdf(invoiceId, true);

  return (
    <div className="flex gap-3">
      <DisplayBtn onClick={onDisplayClick} />
      {DownloadButton && <DownloadButton />}
      <ShareBtn onClick={onShareClick} />
    </div>
  );
};

export default ActionsBtns;
